local ANIMBP_CLASS_PATH =
  "/Game/Art/Character/PC/CH_P_EVE_01/Blueprints/" ..
  "CH_P_EVE_01_AnimBP_New.CH_P_EVE_01_AnimBP_New_C"
local ANIMBP_STUDIO_CLASS_PATH =
  "/Game/Art/Character/PC/CH_P_EVE_01/Blueprints/" ..
  "CH_P_EVE_01_AnimBP_New.CH_P_EVE_01_AnimBP_Studio_C"

local CDO_PATH =
  "/Game/Art/Character/PC/CH_P_EVE_01/Blueprints/" ..
  "CH_P_EVE_01_AnimBP_New.Default__CH_P_EVE_01_AnimBP_New_C"
local CDO_STUDIO_PATH =
  "/Game/Art/Character/PC/CH_P_EVE_01/Blueprints/" ..
  "CH_P_EVE_01_AnimBP_New.Default__CH_P_EVE_01_AnimBP_Studio_C"

local LIVE_IDENTIFIER = "CharacterMesh0.CH_P_EVE_01_AnimBP_New_C"
local LIVE_STUDIO_IDENTIFIER = "Mesh_Body.CH_P_EVE_01_AnimBP_Studio_C"

local SPRING_FIELDS = {
    "AnimGraphNode_SpringBone_24",
    "AnimGraphNode_SpringBone_23",
    "AnimGraphNode_SpringBone_22",
    "AnimGraphNode_SpringBone_21",
    "AnimGraphNode_SpringBone_20",
    "AnimGraphNode_SpringBone_19",
    "AnimGraphNode_SpringBone_18",
    "AnimGraphNode_SpringBone_17",
    "AnimGraphNode_SpringBone_16",
    "AnimGraphNode_SpringBone_15",
    "AnimGraphNode_SpringBone_14",
    "AnimGraphNode_SpringBone_13",
    "AnimGraphNode_SpringBone_12",
    "AnimGraphNode_SpringBone_11",
    "AnimGraphNode_SpringBone_10",
    "AnimGraphNode_SpringBone_9",
    "AnimGraphNode_SpringBone_8",
    "AnimGraphNode_SpringBone_7",
    "AnimGraphNode_SpringBone_6",
    "AnimGraphNode_SpringBone_5",
    "AnimGraphNode_SpringBone_4",
    "AnimGraphNode_SpringBone_3",
    "AnimGraphNode_SpringBone_2",
    "AnimGraphNode_SpringBone_1",
    "AnimGraphNode_SpringBone",
}

local BONE_TWEAKS = {}

local function GetScriptDir()
    local info = debug.getinfo(1, 'S')
    local source = info.source
    local path = source:sub(2)
    return path:match("^(.*[\\/])") or ""
end

local SCRIPT_DIR = GetScriptDir()

local function LoadTweaksFile()
    local tweakPath = SCRIPT_DIR .. "SpringBoneTweaks.lua"
    local ok, tbl = pcall(dofile, tweakPath)
    if ok and type(tbl) == "table" then
        BONE_TWEAKS = tbl
        print(string.format("[Patch] Loaded tweaks from %s", tweakPath))
    else
        print(string.format("[Patch] Failed to load tweaks from %s: %s", tweakPath, tostring(tbl)))
    end
end

local function PatchInstance(inst, label)
    local count = 0
    for _, fieldName in ipairs(SPRING_FIELDS) do
        local node = inst[fieldName]
        if node then
            local boneName
            pcall(function() boneName = node.SpringBone.BoneName:ToString() end)
            if boneName then
                for pat, tweaks in pairs(BONE_TWEAKS) do
                    if boneName:find(pat, 1, true) then
                        for k, v in pairs(tweaks) do node[k] = v end
                        count = count + 1
                        print(string.format("[%s] Patched '%s' (%s)", label, boneName, fieldName))
                        break
                    end
                end
            end
        end
    end
    print(string.format("[%s] Total patched: %d", label, count))
end

local function ApplyAllPatches()
    LoadTweaksFile()

    local bpClass = StaticFindObject(UClass, nil, ANIMBP_CLASS_PATH)
    if not bpClass then
        print(string.format("[Patch] UClass not found: %s", ANIMBP_CLASS_PATH))
    else
        local cdo = StaticFindObject(nil, nil, CDO_PATH)
        if cdo then
            PatchInstance(cdo, "CDO")
        else
            print(string.format("[Patch] CDO not found: %s", CDO_PATH))
        end
    end

    local bpClass = StaticFindObject(UClass, nil, ANIMBP_STUDIO_CLASS_PATH)
    if not bpClass then
        print(string.format("[Patch] UClass not found: %s", ANIMBP_STUDIO_CLASS_PATH))
    else
        local cdo = StaticFindObject(nil, nil, CDO_STUDIO_PATH)
        if cdo then
            PatchInstance(cdo, "CDO")
        else
            print(string.format("[Patch] CDO not found: %s", CDO_STUDIO_PATH))
        end
    end
    
    local liveInst
    for _, inst in ipairs(FindAllOf("CH_P_EVE_01_AnimBP_New_C") or {}) do
        local ok, fullname = pcall(inst.GetFullName, inst)
        if ok and fullname and fullname:find(LIVE_IDENTIFIER, 1, true) then
            liveInst = inst
            break
        end
    end
    if liveInst then
        PatchInstance(liveInst, "Live")
    else
        print(string.format("[Patch] Live instance not found for '%s'", LIVE_IDENTIFIER))
    end
    local liveInst
    for _, inst in ipairs(FindAllOf("CH_P_EVE_01_AnimBP_Studio_C") or {}) do
        local ok, fullname = pcall(inst.GetFullName, inst)
        if ok and fullname and fullname:find(LIVE_STUDIO_IDENTIFIER, 1, true) then
            liveInst = inst
            break
        end
    end
    if liveInst then
        PatchInstance(liveInst, "Live")
    else
        print(string.format("[Patch] Live instance not found for '%s'", LIVE_STUDIO_IDENTIFIER))
    end
end

RegisterKeyBind(112, ApplyAllPatches)

ApplyAllPatches()