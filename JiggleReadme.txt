This is a simple UE4SS mod that updates the Jiggle Physics on Eve allowing for more movement and Jiggle

It updates the breasts, butt and thigh jiggle physics increasing the maximum allowed displacement and adjusts the spring or bounciness.

You can also use the mod to adjust the Jiggle settings and update them in realtime in the game to set it to your own preference by modifying the following file once the mod is installed

Stellar Blade\SB\Binaries\Win64\ue4ss\Mods\JiggleUpdate\Scripts\SpringBoneTweaks.lua

If you change the values in this file you can press F1 in game to have the mod load the new values and apply them to <PERSON> in the game world to see the changes immediately

Settings and what they are for :

MaxDisplacement - This controls the maximum amount of movement from the static position the area can go
SpringStiffness - The amount of force the controlling bone exerts to return to its original position. higher causes faster jiggling
SpringDamping - The amount of drag the bone has when moving causing it to come to a stop. higher reduces back and forth jiggle
bTranslateX/Y/Z - Enables/Disabled displacement on an axis.  Only useful if bUseLocalSpace is true.  X = in and out, Y = up and down, Z = side to side
bRotateX/Y/Z - Do not do anything on the default model, but are included incase anyone makes a custom jiggle outfit. enables/disables rotation per axis
bUseLocalSpace - Controls if motion is calculated in world space or local space.  World space is more realistic, but causes jiggle areas to lag behind the character when running
AverageVelocityFrameCount - controls responsiveness to motion changes.  Higher makes the jiggle require more motion before it starts jiggling


Requirements - UE4SS - https://github.com/Chrisr0/RE-UE4SS/releases

Installation
After installing UE4SS as per the above page, extract the zip into your games main folder
Head to Stellar Blade\SB\Binaries\Win64\ue4ss\Mods\ and open the file mods.txt
Add the following to the top of the file on a new line
JiggleUpdate : 1